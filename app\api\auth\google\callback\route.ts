import { NextRequest, NextResponse } from 'next/server';
import { getTokensFromCode } from '@/lib/google-calendar';
import { supabase } from '@/lib/supabase';

// GET /api/auth/google/callback - Handle Google OAuth callback
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');
  const error = searchParams.get('error');
  
  if (error) {
    console.error('OAuth error:', error);
    return NextResponse.redirect(new URL('/contact?auth=error', request.url));
  }
  
  if (!code) {
    return NextResponse.json(
      { error: 'Authorization code not provided' },
      { status: 400 }
    );
  }
  
  try {
    // Exchange code for tokens
    const tokens = await getTokensFromCode(code);
    
    if (!tokens.access_token || !tokens.refresh_token) {
      throw new Error('Failed to obtain required tokens');
    }
    
    // Store tokens in Supabase (you'll need to create this table)
    const { error: dbError } = await supabase
      .from('calendar_tokens')
      .upsert({
        id: 'admin', // For single user, use a fixed ID
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expires_at: tokens.expiry_date ? new Date(tokens.expiry_date) : null,
        updated_at: new Date()
      });
    
    if (dbError) {
      console.error('Database error:', dbError);
      throw new Error('Failed to store tokens');
    }
    
    // Redirect back to contact page with success
    return NextResponse.redirect(new URL('/contact?auth=success', request.url));
    
  } catch (error) {
    console.error('Error in OAuth callback:', error);
    return NextResponse.redirect(new URL('/contact?auth=error', request.url));
  }
}
