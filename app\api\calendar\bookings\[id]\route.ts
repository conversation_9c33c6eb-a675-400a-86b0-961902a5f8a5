import { NextRequest, NextResponse } from 'next/server';
import { updateCalendarEvent, deleteCalendarEvent, refreshAccessToken } from '@/lib/google-calendar';
import { supabase } from '@/lib/supabase';

// GET /api/calendar/bookings/[id] - Get specific booking
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { data: booking, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', params.id)
      .single();

    if (error || !booking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ booking });

  } catch (error) {
    console.error('Error fetching booking:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/calendar/bookings/[id] - Update booking
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { startTime, endTime, attendeeName, attendeeEmail, summary, description } = body;

    // Get existing booking
    const { data: existingBooking, error: fetchError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', params.id)
      .single();

    if (fetchError || !existingBooking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }

    // Get calendar tokens
    const { data: tokenData, error: tokenError } = await supabase
      .from('calendar_tokens')
      .select('*')
      .eq('id', 'admin')
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json(
        { error: 'Calendar not connected' },
        { status: 401 }
      );
    }

    let accessToken = tokenData.access_token;
    let refreshToken = tokenData.refresh_token;

    // Refresh token if needed
    if (tokenData.expires_at && new Date(tokenData.expires_at) <= new Date()) {
      try {
        const newTokens = await refreshAccessToken(refreshToken);
        accessToken = newTokens.access_token!;

        await supabase
          .from('calendar_tokens')
          .update({
            access_token: accessToken,
            expires_at: newTokens.expiry_date ? new Date(newTokens.expiry_date) : null,
            updated_at: new Date()
          })
          .eq('id', 'admin');
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return NextResponse.json(
          { error: 'Calendar authentication expired' },
          { status: 401 }
        );
      }
    }

    // Update calendar event
    const updateData: any = {};
    if (summary) updateData.summary = summary;
    if (description) updateData.description = description;
    if (startTime) updateData.startTime = new Date(startTime);
    if (endTime) updateData.endTime = new Date(endTime);

    if (Object.keys(updateData).length > 0) {
      await updateCalendarEvent(
        accessToken,
        refreshToken,
        existingBooking.event_id,
        updateData
      );
    }

    // Update booking in database
    const updateFields: any = { updated_at: new Date() };
    if (startTime) updateFields.start_time = startTime;
    if (endTime) updateFields.end_time = endTime;
    if (attendeeName) updateFields.attendee_name = attendeeName;
    if (attendeeEmail) updateFields.attendee_email = attendeeEmail;
    if (summary) updateFields.summary = summary;
    if (description) updateFields.description = description;

    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update(updateFields)
      .eq('id', params.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating booking:', updateError);
      return NextResponse.json(
        { error: 'Failed to update booking' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      booking: updatedBooking,
      message: 'Booking updated successfully'
    });

  } catch (error) {
    console.error('Error updating booking:', error);
    return NextResponse.json(
      { error: 'Failed to update booking' },
      { status: 500 }
    );
  }
}

// DELETE /api/calendar/bookings/[id] - Cancel booking
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get existing booking
    const { data: existingBooking, error: fetchError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', params.id)
      .single();

    if (fetchError || !existingBooking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }

    // Get calendar tokens
    const { data: tokenData, error: tokenError } = await supabase
      .from('calendar_tokens')
      .select('*')
      .eq('id', 'admin')
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json(
        { error: 'Calendar not connected' },
        { status: 401 }
      );
    }

    let accessToken = tokenData.access_token;
    let refreshToken = tokenData.refresh_token;

    // Refresh token if needed
    if (tokenData.expires_at && new Date(tokenData.expires_at) <= new Date()) {
      try {
        const newTokens = await refreshAccessToken(refreshToken);
        accessToken = newTokens.access_token!;

        await supabase
          .from('calendar_tokens')
          .update({
            access_token: accessToken,
            expires_at: newTokens.expiry_date ? new Date(newTokens.expiry_date) : null,
            updated_at: new Date()
          })
          .eq('id', 'admin');
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return NextResponse.json(
          { error: 'Calendar authentication expired' },
          { status: 401 }
        );
      }
    }

    // Delete from Google Calendar
    await deleteCalendarEvent(
      accessToken,
      refreshToken,
      existingBooking.event_id
    );

    // Update booking status to cancelled
    const { error: updateError } = await supabase
      .from('bookings')
      .update({
        status: 'cancelled',
        updated_at: new Date()
      })
      .eq('id', params.id);

    if (updateError) {
      console.error('Error updating booking status:', updateError);
      return NextResponse.json(
        { error: 'Failed to cancel booking' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Booking cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling booking:', error);
    return NextResponse.json(
      { error: 'Failed to cancel booking' },
      { status: 500 }
    );
  }
}
