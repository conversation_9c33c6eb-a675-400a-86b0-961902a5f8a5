import { NextRequest, NextResponse } from 'next/server';
import { createCalendarEvent, refreshAccessToken } from '@/lib/google-calendar';
import { supabase } from '@/lib/supabase';
import { getCalendarSettings, validateBookingTime } from '@/lib/availability';
import { sendBookingConfirmation } from '@/lib/email-notifications';

// POST /api/calendar/book - Create a new booking
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      startTime, 
      endTime, 
      attendeeName, 
      attendeeEmail, 
      summary, 
      description 
    } = body;
    
    // Validate required fields
    if (!startTime || !endTime || !attendeeEmail || !attendeeName) {
      return NextResponse.json(
        { error: 'Missing required fields: startTime, endTime, attendeeEmail, attendeeName' },
        { status: 400 }
      );
    }

    // Get calendar settings and validate booking time
    const settings = await getCalendarSettings();
    const validation = validateBookingTime(
      new Date(startTime),
      new Date(endTime),
      settings
    );

    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }
    
    // Get stored tokens from Supabase
    const { data: tokenData, error: tokenError } = await supabase
      .from('calendar_tokens')
      .select('*')
      .eq('id', 'admin')
      .single();
    
    if (tokenError || !tokenData) {
      return NextResponse.json(
        { error: 'Calendar not connected. Please authenticate first.' },
        { status: 401 }
      );
    }
    
    let accessToken = tokenData.access_token;
    let refreshToken = tokenData.refresh_token;
    
    // Check if token needs refresh
    if (tokenData.expires_at && new Date(tokenData.expires_at) <= new Date()) {
      try {
        const newTokens = await refreshAccessToken(refreshToken);
        accessToken = newTokens.access_token!;
        
        // Update tokens in database
        await supabase
          .from('calendar_tokens')
          .update({
            access_token: accessToken,
            expires_at: newTokens.expiry_date ? new Date(newTokens.expiry_date) : null,
            updated_at: new Date()
          })
          .eq('id', 'admin');
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return NextResponse.json(
          { error: 'Calendar authentication expired. Please re-authenticate.' },
          { status: 401 }
        );
      }
    }
    
    // Create calendar event
    const event = await createCalendarEvent(
      accessToken,
      refreshToken,
      {
        summary: summary || `Meeting with ${attendeeName}`,
        description: description || `Consultation call with ${attendeeName} (${attendeeEmail})`,
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        attendeeEmail,
        attendeeName
      }
    );
    
    // Store booking in Supabase
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .insert({
        event_id: event.id,
        attendee_name: attendeeName,
        attendee_email: attendeeEmail,
        start_time: startTime,
        end_time: endTime,
        summary: summary || `Meeting with ${attendeeName}`,
        description: description,
        status: 'confirmed',
        created_at: new Date()
      })
      .select()
      .single();
    
    if (bookingError) {
      console.error('Error storing booking:', bookingError);
      // Event was created in calendar, but failed to store in DB
      // You might want to implement cleanup logic here
    }

    // Send confirmation email (don't fail the booking if email fails)
    if (booking) {
      try {
        await sendBookingConfirmation({
          id: booking.id,
          attendeeName,
          attendeeEmail,
          startTime: new Date(startTime),
          endTime: new Date(endTime),
          summary: summary || `Meeting with ${attendeeName}`,
          description: description
        });
      } catch (emailError) {
        console.error('Error sending confirmation email:', emailError);
        // Don't fail the booking if email fails
      }
    }

    return NextResponse.json({
      success: true,
      event,
      booking,
      message: 'Booking created successfully'
    });
    
  } catch (error) {
    console.error('Error creating booking:', error);
    return NextResponse.json(
      { error: 'Failed to create booking' },
      { status: 500 }
    );
  }
}
