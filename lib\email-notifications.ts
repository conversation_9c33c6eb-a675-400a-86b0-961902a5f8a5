import nodemailer from 'nodemailer';
import { format } from 'date-fns';

// Email configuration - you'll need to add these to your .env.local
const EMAIL_HOST = process.env.EMAIL_HOST || 'smtp.gmail.com';
const EMAIL_PORT = parseInt(process.env.EMAIL_PORT || '587');
const EMAIL_USER = process.env.EMAIL_USER;
const EMAIL_PASS = process.env.EMAIL_PASS;
const FROM_EMAIL = process.env.FROM_EMAIL || EMAIL_USER;
const FROM_NAME = process.env.FROM_NAME || 'UpZera';

// Create transporter
const createTransporter = () => {
  if (!EMAIL_USER || !EMAIL_PASS) {
    console.warn('Email credentials not configured. Email notifications will not be sent.');
    return null;
  }

  return nodemailer.createTransporter({
    host: EMAIL_HOST,
    port: EMAIL_PORT,
    secure: EMAIL_PORT === 465, // true for 465, false for other ports
    auth: {
      user: EMAIL_USER,
      pass: EMAIL_PASS,
    },
  });
};

interface BookingDetails {
  id: string;
  attendeeName: string;
  attendeeEmail: string;
  startTime: Date;
  endTime: Date;
  summary: string;
  description?: string;
}

// Send booking confirmation email
export async function sendBookingConfirmation(booking: BookingDetails) {
  const transporter = createTransporter();
  if (!transporter) return false;

  try {
    const startTimeFormatted = format(booking.startTime, 'EEEE, MMMM d, yyyy \'at\' h:mm a');
    const endTimeFormatted = format(booking.endTime, 'h:mm a');

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Booking Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #7c3aed, #a855f7); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 20px; border-radius: 0 0 8px 8px; }
          .booking-details { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Booking Confirmed! 🎉</h1>
          </div>
          <div class="content">
            <p>Hi ${booking.attendeeName},</p>
            <p>Your meeting has been successfully scheduled. Here are the details:</p>
            
            <div class="booking-details">
              <h3>${booking.summary}</h3>
              <p><strong>Date & Time:</strong> ${startTimeFormatted} - ${endTimeFormatted}</p>
              <p><strong>Duration:</strong> ${Math.round((booking.endTime.getTime() - booking.startTime.getTime()) / (1000 * 60))} minutes</p>
              ${booking.description ? `<p><strong>Notes:</strong> ${booking.description}</p>` : ''}
            </div>

            <p>We'll send you a calendar invitation shortly. If you need to reschedule or cancel, please contact us as soon as possible.</p>
            
            <p>Looking forward to speaking with you!</p>
            
            <p>Best regards,<br>The UpZera Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const textContent = `
      Booking Confirmed!

      Hi ${booking.attendeeName},

      Your meeting has been successfully scheduled:

      ${booking.summary}
      Date & Time: ${startTimeFormatted} - ${endTimeFormatted}
      Duration: ${Math.round((booking.endTime.getTime() - booking.startTime.getTime()) / (1000 * 60))} minutes
      ${booking.description ? `Notes: ${booking.description}` : ''}

      We'll send you a calendar invitation shortly. If you need to reschedule or cancel, please contact us as soon as possible.

      Looking forward to speaking with you!

      Best regards,
      The UpZera Team
    `;

    await transporter.sendMail({
      from: `"${FROM_NAME}" <${FROM_EMAIL}>`,
      to: booking.attendeeEmail,
      subject: `Meeting Confirmed - ${format(booking.startTime, 'MMM d, yyyy')}`,
      text: textContent,
      html: htmlContent,
    });

    console.log(`Booking confirmation sent to ${booking.attendeeEmail}`);
    return true;

  } catch (error) {
    console.error('Error sending booking confirmation:', error);
    return false;
  }
}

// Send booking cancellation email
export async function sendBookingCancellation(booking: BookingDetails) {
  const transporter = createTransporter();
  if (!transporter) return false;

  try {
    const startTimeFormatted = format(booking.startTime, 'EEEE, MMMM d, yyyy \'at\' h:mm a');

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Booking Cancelled</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #ef4444; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 20px; border-radius: 0 0 8px 8px; }
          .booking-details { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Booking Cancelled</h1>
          </div>
          <div class="content">
            <p>Hi ${booking.attendeeName},</p>
            <p>Your meeting scheduled for <strong>${startTimeFormatted}</strong> has been cancelled.</p>
            
            <div class="booking-details">
              <h3>${booking.summary}</h3>
              <p><strong>Original Date & Time:</strong> ${startTimeFormatted}</p>
            </div>

            <p>If you'd like to reschedule, please visit our website to book a new appointment.</p>
            
            <p>We apologize for any inconvenience.</p>
            
            <p>Best regards,<br>The UpZera Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const textContent = `
      Booking Cancelled

      Hi ${booking.attendeeName},

      Your meeting scheduled for ${startTimeFormatted} has been cancelled.

      ${booking.summary}
      Original Date & Time: ${startTimeFormatted}

      If you'd like to reschedule, please visit our website to book a new appointment.

      We apologize for any inconvenience.

      Best regards,
      The UpZera Team
    `;

    await transporter.sendMail({
      from: `"${FROM_NAME}" <${FROM_EMAIL}>`,
      to: booking.attendeeEmail,
      subject: `Meeting Cancelled - ${format(booking.startTime, 'MMM d, yyyy')}`,
      text: textContent,
      html: htmlContent,
    });

    console.log(`Booking cancellation sent to ${booking.attendeeEmail}`);
    return true;

  } catch (error) {
    console.error('Error sending booking cancellation:', error);
    return false;
  }
}

// Send booking reminder email (to be called by a cron job or scheduled task)
export async function sendBookingReminder(booking: BookingDetails) {
  const transporter = createTransporter();
  if (!transporter) return false;

  try {
    const startTimeFormatted = format(booking.startTime, 'EEEE, MMMM d, yyyy \'at\' h:mm a');
    const timeUntil = Math.round((booking.startTime.getTime() - new Date().getTime()) / (1000 * 60 * 60));

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Meeting Reminder</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f59e0b; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 20px; border-radius: 0 0 8px 8px; }
          .booking-details { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Meeting Reminder ⏰</h1>
          </div>
          <div class="content">
            <p>Hi ${booking.attendeeName},</p>
            <p>This is a friendly reminder about your upcoming meeting in approximately ${timeUntil} hours.</p>
            
            <div class="booking-details">
              <h3>${booking.summary}</h3>
              <p><strong>Date & Time:</strong> ${startTimeFormatted}</p>
            </div>

            <p>We look forward to speaking with you!</p>
            
            <p>Best regards,<br>The UpZera Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    await transporter.sendMail({
      from: `"${FROM_NAME}" <${FROM_EMAIL}>`,
      to: booking.attendeeEmail,
      subject: `Meeting Reminder - Tomorrow at ${format(booking.startTime, 'h:mm a')}`,
      html: htmlContent,
    });

    console.log(`Booking reminder sent to ${booking.attendeeEmail}`);
    return true;

  } catch (error) {
    console.error('Error sending booking reminder:', error);
    return false;
  }
}
