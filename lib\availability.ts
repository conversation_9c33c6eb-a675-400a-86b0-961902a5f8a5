import { supabase } from './supabase';

export interface CalendarSettings {
  working_hours_start: number;
  working_hours_end: number;
  slot_duration: number;
  timezone: string;
  allow_weekends: boolean;
  advance_booking_days: number;
  buffer_time: number;
}

export interface TimeSlot {
  start: Date;
  end: Date;
  duration: number;
}

export interface BusyTime {
  start: string;
  end: string;
}

// Get calendar settings from database
export async function getCalendarSettings(): Promise<CalendarSettings> {
  try {
    const { data, error } = await supabase
      .from('calendar_settings')
      .select('*')
      .eq('id', 'default')
      .single();

    if (error || !data) {
      // Return default settings if not found
      return {
        working_hours_start: 9,
        working_hours_end: 17,
        slot_duration: 30,
        timezone: 'Europe/Vilnius',
        allow_weekends: false,
        advance_booking_days: 30,
        buffer_time: 0
      };
    }

    return data;
  } catch (error) {
    console.error('Error fetching calendar settings:', error);
    // Return default settings on error
    return {
      working_hours_start: 9,
      working_hours_end: 17,
      slot_duration: 30,
      timezone: 'Europe/Vilnius',
      allow_weekends: false,
      advance_booking_days: 30,
      buffer_time: 0
    };
  }
}

// Generate time slots for a given date range
export function generateTimeSlots(
  startDate: Date,
  endDate: Date,
  settings: CalendarSettings
): TimeSlot[] {
  const slots: TimeSlot[] = [];
  const current = new Date(startDate);
  const now = new Date();

  while (current < endDate) {
    // Skip weekends if not allowed
    const dayOfWeek = current.getDay();
    if (!settings.allow_weekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
      current.setDate(current.getDate() + 1);
      current.setHours(settings.working_hours_start, 0, 0, 0);
      continue;
    }

    // Check if current time is within working hours
    const currentHour = current.getHours();
    const currentMinute = current.getMinutes();
    
    if (currentHour >= settings.working_hours_start && currentHour < settings.working_hours_end) {
      // Skip slots that are in the past
      if (current > now) {
        const slotEnd = new Date(current.getTime() + settings.slot_duration * 60000);
        
        // Make sure the slot doesn't extend beyond working hours
        if (slotEnd.getHours() <= settings.working_hours_end || 
            (slotEnd.getHours() === settings.working_hours_end && slotEnd.getMinutes() === 0)) {
          slots.push({
            start: new Date(current),
            end: slotEnd,
            duration: settings.slot_duration
          });
        }
      }
    }

    // Move to next slot
    current.setMinutes(current.getMinutes() + settings.slot_duration);

    // If we've passed working hours, move to next day
    if (current.getHours() >= settings.working_hours_end) {
      current.setDate(current.getDate() + 1);
      current.setHours(settings.working_hours_start, 0, 0, 0);
    }
  }

  return slots;
}

// Filter out busy times from available slots
export function filterBusySlots(
  availableSlots: TimeSlot[],
  busyTimes: BusyTime[],
  bufferTime: number = 0
): TimeSlot[] {
  return availableSlots.filter(slot => {
    // Add buffer time to slot
    const slotStart = new Date(slot.start.getTime() - bufferTime * 60000);
    const slotEnd = new Date(slot.end.getTime() + bufferTime * 60000);

    // Check if slot conflicts with any busy time
    return !busyTimes.some(busyTime => {
      const busyStart = new Date(busyTime.start);
      const busyEnd = new Date(busyTime.end);

      // Check for overlap
      return slotStart < busyEnd && slotEnd > busyStart;
    });
  });
}

// Check if a specific time slot is available
export function isSlotAvailable(
  slot: TimeSlot,
  busyTimes: BusyTime[],
  bufferTime: number = 0
): boolean {
  const slotStart = new Date(slot.start.getTime() - bufferTime * 60000);
  const slotEnd = new Date(slot.end.getTime() + bufferTime * 60000);

  return !busyTimes.some(busyTime => {
    const busyStart = new Date(busyTime.start);
    const busyEnd = new Date(busyTime.end);

    return slotStart < busyEnd && slotEnd > busyStart;
  });
}

// Get existing bookings from database for a date range
export async function getExistingBookings(
  startDate: Date,
  endDate: Date
): Promise<BusyTime[]> {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .select('start_time, end_time')
      .gte('start_time', startDate.toISOString())
      .lte('end_time', endDate.toISOString())
      .eq('status', 'confirmed');

    if (error) {
      console.error('Error fetching existing bookings:', error);
      return [];
    }

    return (data || []).map(booking => ({
      start: booking.start_time,
      end: booking.end_time
    }));
  } catch (error) {
    console.error('Error fetching existing bookings:', error);
    return [];
  }
}

// Combine Google Calendar busy times with local bookings
export function combineBusyTimes(
  googleBusyTimes: BusyTime[],
  localBookings: BusyTime[]
): BusyTime[] {
  const allBusyTimes = [...googleBusyTimes, ...localBookings];
  
  // Sort by start time
  allBusyTimes.sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());
  
  // Merge overlapping busy times
  const merged: BusyTime[] = [];
  
  for (const busyTime of allBusyTimes) {
    if (merged.length === 0) {
      merged.push(busyTime);
      continue;
    }
    
    const last = merged[merged.length - 1];
    const lastEnd = new Date(last.end);
    const currentStart = new Date(busyTime.start);
    
    // If current busy time overlaps with the last one, merge them
    if (currentStart <= lastEnd) {
      const currentEnd = new Date(busyTime.end);
      if (currentEnd > lastEnd) {
        last.end = busyTime.end;
      }
    } else {
      merged.push(busyTime);
    }
  }
  
  return merged;
}

// Validate booking time against business rules
export function validateBookingTime(
  startTime: Date,
  endTime: Date,
  settings: CalendarSettings
): { valid: boolean; error?: string } {
  const now = new Date();
  
  // Check if booking is in the past
  if (startTime <= now) {
    return { valid: false, error: 'Cannot book appointments in the past' };
  }
  
  // Check if booking is too far in advance
  const maxAdvanceDate = new Date();
  maxAdvanceDate.setDate(maxAdvanceDate.getDate() + settings.advance_booking_days);
  
  if (startTime > maxAdvanceDate) {
    return { 
      valid: false, 
      error: `Cannot book more than ${settings.advance_booking_days} days in advance` 
    };
  }
  
  // Check if booking is within working hours
  const startHour = startTime.getHours();
  const endHour = endTime.getHours();
  const endMinute = endTime.getMinutes();
  
  if (startHour < settings.working_hours_start || 
      startHour >= settings.working_hours_end ||
      endHour > settings.working_hours_end ||
      (endHour === settings.working_hours_end && endMinute > 0)) {
    return { 
      valid: false, 
      error: `Bookings must be within working hours (${settings.working_hours_start}:00 - ${settings.working_hours_end}:00)` 
    };
  }
  
  // Check if booking is on weekend (if not allowed)
  const dayOfWeek = startTime.getDay();
  if (!settings.allow_weekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
    return { valid: false, error: 'Weekend bookings are not allowed' };
  }
  
  // Check duration
  const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
  if (duration !== settings.slot_duration) {
    return { 
      valid: false, 
      error: `Booking duration must be ${settings.slot_duration} minutes` 
    };
  }
  
  return { valid: true };
}
