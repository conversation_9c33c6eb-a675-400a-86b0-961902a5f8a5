"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar, Clock, User, Mail, CheckCircle, AlertCircle } from 'lucide-react';
import { format, addDays, startOfDay } from 'date-fns';

interface TimeSlot {
  start: Date;
  end: Date;
  duration: number;
}

interface BookingFormData {
  name: string;
  email: string;
  message: string;
}

interface BookingCalendarProps {
  className?: string;
}

export default function BookingCalendar({ className = '' }: BookingCalendarProps) {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [bookingStep, setBookingStep] = useState<'date' | 'time' | 'form' | 'success'>('date');
  const [formData, setFormData] = useState<BookingFormData>({
    name: '',
    email: '',
    message: ''
  });
  const [error, setError] = useState<string | null>(null);

  // Generate next 30 days for date selection
  const availableDates = Array.from({ length: 30 }, (_, i) => {
    const date = addDays(new Date(), i + 1); // Start from tomorrow
    return startOfDay(date);
  }).filter(date => {
    // Filter out weekends (optional)
    const dayOfWeek = date.getDay();
    return dayOfWeek !== 0 && dayOfWeek !== 6; // Exclude Sunday (0) and Saturday (6)
  });

  // Fetch available slots when date is selected
  useEffect(() => {
    if (selectedDate) {
      fetchAvailableSlots(selectedDate);
    }
  }, [selectedDate]);

  const fetchAvailableSlots = async (date: Date) => {
    setLoading(true);
    setError(null);
    
    try {
      const startDate = startOfDay(date);
      const endDate = new Date(startDate);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await fetch(
        `/api/calendar/availability?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&duration=30`
      );
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch availability');
      }
      
      const data = await response.json();
      setAvailableSlots(data.slots.map((slot: any) => ({
        start: new Date(slot.start),
        end: new Date(slot.end),
        duration: slot.duration
      })));
    } catch (error) {
      console.error('Error fetching slots:', error);
      setError(error instanceof Error ? error.message : 'Failed to load available times');
      setAvailableSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setSelectedSlot(null);
    setBookingStep('time');
  };

  const handleSlotSelect = (slot: TimeSlot) => {
    setSelectedSlot(slot);
    setBookingStep('form');
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedSlot || !formData.name || !formData.email) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/calendar/book', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          startTime: selectedSlot.start.toISOString(),
          endTime: selectedSlot.end.toISOString(),
          attendeeName: formData.name,
          attendeeEmail: formData.email,
          summary: `Consultation with ${formData.name}`,
          description: formData.message || `Meeting with ${formData.name} (${formData.email})`
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to book appointment');
      }

      setBookingStep('success');
    } catch (error) {
      console.error('Error booking appointment:', error);
      setError(error instanceof Error ? error.message : 'Failed to book appointment');
    } finally {
      setLoading(false);
    }
  };

  const resetBooking = () => {
    setSelectedDate(null);
    setSelectedSlot(null);
    setBookingStep('date');
    setFormData({ name: '', email: '', message: '' });
    setError(null);
  };

  return (
    <div className={`bg-purple-900/30 backdrop-blur-sm rounded-2xl p-6 border border-purple-700/20 ${className}`}>
      {error && (
        <div className="mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <p className="text-red-200">{error}</p>
        </div>
      )}

      {bookingStep === 'date' && (
        <div>
          <div className="flex items-center gap-2 mb-6">
            <Calendar className="w-6 h-6 text-purple-400" />
            <h3 className="text-xl font-semibold text-white">Select a Date</h3>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {availableDates.map((date) => (
              <Button
                key={date.toISOString()}
                variant="outline"
                className="h-auto p-3 border-purple-600/30 hover:border-purple-500 hover:bg-purple-800/30 text-white"
                onClick={() => handleDateSelect(date)}
              >
                <div className="text-center">
                  <div className="text-sm font-medium">
                    {format(date, 'EEE')}
                  </div>
                  <div className="text-lg">
                    {format(date, 'd')}
                  </div>
                  <div className="text-xs text-purple-200">
                    {format(date, 'MMM')}
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      )}

      {bookingStep === 'time' && selectedDate && (
        <div>
          <div className="flex items-center gap-2 mb-6">
            <Clock className="w-6 h-6 text-purple-400" />
            <h3 className="text-xl font-semibold text-white">
              Select Time - {format(selectedDate, 'EEEE, MMMM d, yyyy')}
            </h3>
          </div>
          
          <Button
            variant="ghost"
            onClick={() => setBookingStep('date')}
            className="mb-4 text-purple-300 hover:text-white"
          >
            ← Back to date selection
          </Button>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
              <span className="ml-2 text-purple-200">Loading available times...</span>
            </div>
          ) : availableSlots.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {availableSlots.map((slot, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="border-purple-600/30 hover:border-purple-500 hover:bg-purple-800/30 text-white"
                  onClick={() => handleSlotSelect(slot)}
                >
                  {format(slot.start, 'h:mm a')}
                </Button>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-purple-200">No available times for this date.</p>
              <Button
                variant="ghost"
                onClick={() => setBookingStep('date')}
                className="mt-2 text-purple-300 hover:text-white"
              >
                Choose a different date
              </Button>
            </div>
          )}
        </div>
      )}

      {bookingStep === 'form' && selectedSlot && (
        <div>
          <div className="flex items-center gap-2 mb-6">
            <User className="w-6 h-6 text-purple-400" />
            <h3 className="text-xl font-semibold text-white">Your Information</h3>
          </div>
          
          <div className="mb-4 p-4 bg-purple-800/30 rounded-lg">
            <p className="text-purple-200 text-sm">Selected time:</p>
            <p className="text-white font-medium">
              {format(selectedSlot.start, 'EEEE, MMMM d, yyyy')} at {format(selectedSlot.start, 'h:mm a')}
            </p>
          </div>

          <Button
            variant="ghost"
            onClick={() => setBookingStep('time')}
            className="mb-4 text-purple-300 hover:text-white"
          >
            ← Back to time selection
          </Button>

          <form onSubmit={handleFormSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-purple-200 mb-2">
                Full Name *
              </label>
              <Input
                type="text"
                required
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="bg-purple-900/50 border-purple-600/30 text-white placeholder-purple-300"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-purple-200 mb-2">
                Email Address *
              </label>
              <Input
                type="email"
                required
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="bg-purple-900/50 border-purple-600/30 text-white placeholder-purple-300"
                placeholder="Enter your email address"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-purple-200 mb-2">
                Message (Optional)
              </label>
              <Textarea
                value={formData.message}
                onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                className="bg-purple-900/50 border-purple-600/30 text-white placeholder-purple-300"
                placeholder="Tell us what you'd like to discuss..."
                rows={3}
              />
            </div>

            <Button
              type="submit"
              disabled={loading || !formData.name || !formData.email}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Booking...
                </>
              ) : (
                'Confirm Booking'
              )}
            </Button>
          </form>
        </div>
      )}

      {bookingStep === 'success' && (
        <div className="text-center py-8">
          <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
          <h3 className="text-2xl font-semibold text-white mb-2">Booking Confirmed!</h3>
          <p className="text-purple-200 mb-4">
            Your meeting has been scheduled for {selectedSlot && format(selectedSlot.start, 'EEEE, MMMM d, yyyy')} at {selectedSlot && format(selectedSlot.start, 'h:mm a')}.
          </p>
          <p className="text-purple-300 text-sm mb-6">
            You'll receive a calendar invitation at {formData.email} with all the details.
          </p>
          <Button
            onClick={resetBooking}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            Book Another Meeting
          </Button>
        </div>
      )}
    </div>
  );
}
