"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { format, addDays, startOfDay } from 'date-fns';

interface TimeSlot {
  start: Date;
  end: Date;
  duration: number;
}

interface ChatbotBookingWidgetProps {
  onBookingComplete?: (booking: any) => void;
  className?: string;
}

export default function ChatbotBookingWidget({ 
  onBookingComplete, 
  className = '' 
}: ChatbotBookingWidgetProps) {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'date' | 'time' | 'form' | 'success'>('date');
  const [formData, setFormData] = useState({ name: '', email: '' });
  const [error, setError] = useState<string | null>(null);

  // Generate next 14 days for compact display
  const availableDates = Array.from({ length: 14 }, (_, i) => {
    const date = addDays(new Date(), i + 1);
    return startOfDay(date);
  }).filter(date => {
    const dayOfWeek = date.getDay();
    return dayOfWeek !== 0 && dayOfWeek !== 6; // Exclude weekends
  });

  useEffect(() => {
    if (selectedDate) {
      fetchAvailableSlots(selectedDate);
    }
  }, [selectedDate]);

  const fetchAvailableSlots = async (date: Date) => {
    setLoading(true);
    setError(null);
    
    try {
      const startDate = startOfDay(date);
      const endDate = new Date(startDate);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await fetch(
        `/api/calendar/availability?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}&duration=30`
      );
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch availability');
      }
      
      const data = await response.json();
      setAvailableSlots(data.slots.map((slot: any) => ({
        start: new Date(slot.start),
        end: new Date(slot.end),
        duration: slot.duration
      })));
    } catch (error) {
      console.error('Error fetching slots:', error);
      setError(error instanceof Error ? error.message : 'Failed to load available times');
      setAvailableSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const handleBooking = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedSlot || !formData.name || !formData.email) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/calendar/book', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          startTime: selectedSlot.start.toISOString(),
          endTime: selectedSlot.end.toISOString(),
          attendeeName: formData.name,
          attendeeEmail: formData.email,
          summary: `Consultation with ${formData.name}`,
          description: `Meeting with ${formData.name} (${formData.email})`
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to book appointment');
      }

      const booking = await response.json();
      setStep('success');
      onBookingComplete?.(booking);
    } catch (error) {
      console.error('Error booking appointment:', error);
      setError(error instanceof Error ? error.message : 'Failed to book appointment');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg p-4 border border-gray-200 max-w-md ${className}`}>
      {error && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm flex items-center gap-2">
          <AlertCircle className="w-4 h-4" />
          {error}
        </div>
      )}

      {step === 'date' && (
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Calendar className="w-5 h-5 text-purple-600" />
            <h4 className="font-semibold text-gray-800">Select Date</h4>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            {availableDates.slice(0, 6).map((date) => (
              <Button
                key={date.toISOString()}
                variant="outline"
                size="sm"
                className="h-auto p-2 text-xs"
                onClick={() => {
                  setSelectedDate(date);
                  setStep('time');
                }}
              >
                <div className="text-center">
                  <div className="font-medium">{format(date, 'EEE')}</div>
                  <div>{format(date, 'MMM d')}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      )}

      {step === 'time' && selectedDate && (
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Clock className="w-5 h-5 text-purple-600" />
            <h4 className="font-semibold text-gray-800">
              {format(selectedDate, 'MMM d')} - Select Time
            </h4>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setStep('date')}
            className="mb-2 text-xs"
          >
            ← Back
          </Button>

          {loading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-600"></div>
            </div>
          ) : availableSlots.length > 0 ? (
            <div className="grid grid-cols-2 gap-2">
              {availableSlots.slice(0, 6).map((slot, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setSelectedSlot(slot);
                    setStep('form');
                  }}
                >
                  {format(slot.start, 'h:mm a')}
                </Button>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-600 text-center py-2">
              No times available
            </p>
          )}
        </div>
      )}

      {step === 'form' && selectedSlot && (
        <div>
          <div className="mb-3">
            <h4 className="font-semibold text-gray-800 mb-1">Confirm Booking</h4>
            <p className="text-sm text-gray-600">
              {format(selectedSlot.start, 'MMM d, h:mm a')}
            </p>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setStep('time')}
            className="mb-2 text-xs"
          >
            ← Back
          </Button>

          <form onSubmit={handleBooking} className="space-y-3">
            <Input
              type="text"
              placeholder="Your name"
              required
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="text-sm"
            />
            <Input
              type="email"
              placeholder="Your email"
              required
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="text-sm"
            />
            <Button
              type="submit"
              disabled={loading}
              className="w-full bg-purple-600 hover:bg-purple-700 text-sm"
            >
              {loading ? 'Booking...' : 'Confirm'}
            </Button>
          </form>
        </div>
      )}

      {step === 'success' && (
        <div className="text-center py-4">
          <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
          <h4 className="font-semibold text-gray-800 mb-1">Booked!</h4>
          <p className="text-sm text-gray-600">
            Check your email for details
          </p>
        </div>
      )}
    </div>
  );
}
