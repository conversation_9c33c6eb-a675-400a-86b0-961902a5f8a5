# 🔒 Security Guide - Environment Variables

## ⚠️ CRITICAL SECURITY ISSUE FIXED

I found that your **real API keys were being committed to Git** in the `.env` file. This is a major security risk as anyone with access to your repository could see your:
- Mailgun API keys
- OpenAI API keys  
- Database credentials
- Other sensitive information

## ✅ What I Fixed

1. **Moved all real secrets to `.env.local`** (which is ignored by Git)
2. **Converted `.env` to a template file** with example values
3. **Updated `.gitignore`** to properly handle environment files
4. **Consolidated all environment variables** into one secure location

## 📁 File Structure Now

```
.env          # Template file with example values (SAFE to commit)
.env.local    # Your actual secrets (NEVER committed to Git)
.gitignore    # Properly configured to ignore .env.local
```

## 🔧 How Environment Variables Work in Next.js

- **`.env`** - Template/example file, committed to Git
- **`.env.local`** - Your actual secrets, ignored by Git
- **`.env.production`** - Production-specific variables (also ignored)

Next.js automatically loads variables in this order:
1. `.env.local` (highest priority)
2. `.env.production` or `.env.development`
3. `.env` (lowest priority)

## 🚨 Immediate Actions Required

### 1. Regenerate Compromised API Keys
Since your keys were in Git history, you should regenerate:

**Mailgun:**
- Go to Mailgun dashboard → API Keys → Regenerate

**OpenAI:**
- Go to OpenAI dashboard → API Keys → Create new key
- Delete the old key

**Supabase:**
- Go to Supabase dashboard → Settings → API
- Regenerate the anon key if needed

### 2. Update Your .env.local
Replace the placeholder values in `.env.local` with your new API keys:

```env
# Update these with your NEW keys
MAILGUN_API_KEY=your-new-mailgun-key
NEXT_PUBLIC_OPENAI_API_KEY=your-new-openai-key
```

### 3. Clean Git History (Optional but Recommended)
To remove the keys from Git history:

```bash
# WARNING: This rewrites Git history - coordinate with team members
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch .env' \
  --prune-empty --tag-name-filter cat -- --all

# Force push to update remote
git push origin --force --all
```

## 🛡️ Security Best Practices

### ✅ DO:
- Keep all secrets in `.env.local`
- Use different API keys for development/production
- Regularly rotate API keys
- Use environment-specific variables
- Review what's being committed before pushing

### ❌ DON'T:
- Commit real API keys to Git
- Share `.env.local` files
- Use production keys in development
- Hardcode secrets in your code
- Ignore security warnings

## 🚀 Deployment Security

### For Production (Netlify/Vercel):
1. **Don't upload `.env.local`** to your hosting platform
2. **Set environment variables** in your hosting dashboard:
   - Netlify: Site settings → Environment variables
   - Vercel: Project settings → Environment Variables

### Environment Variable Names:
- `NEXT_PUBLIC_*` variables are exposed to the browser
- Regular variables are server-side only
- Only use `NEXT_PUBLIC_` for non-sensitive data

## 📋 Current Environment Variables

### Public (Browser-accessible):
```env
NEXT_PUBLIC_SUPABASE_URL=...
NEXT_PUBLIC_SUPABASE_KEY=...  # Anon key is safe to expose
NEXT_PUBLIC_OPENAI_API_KEY=... # Consider moving to server-side
NEXT_PUBLIC_CHATBOT_URL=...
```

### Private (Server-side only):
```env
MAILGUN_API_KEY=...
GOOGLE_CLIENT_SECRET=...
EMAIL_PASS=...
```

## ⚠️ Recommendation: Move OpenAI Key Server-Side

Your OpenAI key is currently `NEXT_PUBLIC_*` which means it's visible in the browser. Consider:

1. Creating server-side API routes for OpenAI calls
2. Removing `NEXT_PUBLIC_` prefix
3. Making calls from your backend instead of frontend

## 🔍 How to Check What's Exposed

In your browser console, type:
```javascript
console.log(process.env)
```

Only `NEXT_PUBLIC_*` variables should appear.

## 📞 Next Steps

1. ✅ **Fixed**: Environment variables consolidated and secured
2. 🔄 **Action Required**: Regenerate compromised API keys
3. 🔄 **Action Required**: Update `.env.local` with new keys
4. 🔄 **Optional**: Clean Git history
5. 🔄 **Recommended**: Move OpenAI key server-side

Your calendar integration will work once you:
1. Set up Google Calendar API credentials
2. Update the placeholder values in `.env.local`
3. Run the database setup from `CALENDAR_SETUP.md`

The security issue is now fixed, and all your secrets are properly protected! 🔒
