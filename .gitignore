# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# Keep .env as template, but ignore .env.local with real secrets
# .env - template file (safe to commit)
# .env.local - actual secrets (never commit)

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
