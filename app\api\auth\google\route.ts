import { NextRequest, NextResponse } from 'next/server';
import { getAuthUrl } from '@/lib/google-calendar';

// GET /api/auth/google - Initiate Google OAuth flow
export async function GET() {
  try {
    const authUrl = getAuthUrl();
    
    return NextResponse.json({ 
      authUrl,
      message: 'Redirect to this URL to authenticate with Google Calendar' 
    });
  } catch (error) {
    console.error('Error generating auth URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate authentication URL' },
      { status: 500 }
    );
  }
}
