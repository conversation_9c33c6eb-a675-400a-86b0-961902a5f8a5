import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

// Google Calendar configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET;
const GOOGLE_REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/auth/google/callback';

// Scopes needed for calendar access
const SCOPES = [
  'https://www.googleapis.com/auth/calendar.readonly',
  'https://www.googleapis.com/auth/calendar.events'
];

// Create OAuth2 client
export function createOAuth2Client() {
  return new google.auth.OAuth2(
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    GOOGLE_REDIRECT_URI
  );
}

// Generate authorization URL
export function getAuthUrl() {
  const oauth2Client = createOAuth2Client();
  
  return oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
    prompt: 'consent'
  });
}

// Exchange authorization code for tokens
export async function getTokensFromCode(code: string) {
  const oauth2Client = createOAuth2Client();
  
  try {
    const { tokens } = await oauth2Client.getToken(code);
    return tokens;
  } catch (error) {
    console.error('Error getting tokens:', error);
    throw new Error('Failed to exchange authorization code for tokens');
  }
}

// Create authenticated calendar client
export function createCalendarClient(accessToken: string, refreshToken?: string) {
  const oauth2Client = createOAuth2Client();
  
  oauth2Client.setCredentials({
    access_token: accessToken,
    refresh_token: refreshToken
  });
  
  return google.calendar({ version: 'v3', auth: oauth2Client });
}

// Get available time slots for a given date range
export async function getAvailableSlots(
  accessToken: string,
  refreshToken: string,
  startDate: Date,
  endDate: Date,
  duration: number = 30, // duration in minutes
  workingHours = { start: 9, end: 17 }, // 9 AM to 5 PM
  calendarId = 'primary'
) {
  try {
    const calendar = createCalendarClient(accessToken, refreshToken);
    
    // Get busy times from calendar
    const freeBusyResponse = await calendar.freebusy.query({
      requestBody: {
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        items: [{ id: calendarId }]
      }
    });
    
    const busyTimes = freeBusyResponse.data.calendars?.[calendarId]?.busy || [];
    
    // Generate available slots
    const availableSlots = [];
    const current = new Date(startDate);
    
    while (current < endDate) {
      // Skip weekends (optional - can be configured)
      if (current.getDay() === 0 || current.getDay() === 6) {
        current.setDate(current.getDate() + 1);
        current.setHours(workingHours.start, 0, 0, 0);
        continue;
      }
      
      // Check if current time is within working hours
      const currentHour = current.getHours();
      if (currentHour >= workingHours.start && currentHour < workingHours.end) {
        const slotEnd = new Date(current.getTime() + duration * 60000);
        
        // Check if slot conflicts with busy times
        const isAvailable = !busyTimes.some(busyTime => {
          const busyStart = new Date(busyTime.start!);
          const busyEnd = new Date(busyTime.end!);
          
          return (current < busyEnd && slotEnd > busyStart);
        });
        
        if (isAvailable) {
          availableSlots.push({
            start: new Date(current),
            end: new Date(slotEnd),
            duration
          });
        }
      }
      
      // Move to next slot (every 30 minutes)
      current.setMinutes(current.getMinutes() + duration);
      
      // If we've passed working hours, move to next day
      if (current.getHours() >= workingHours.end) {
        current.setDate(current.getDate() + 1);
        current.setHours(workingHours.start, 0, 0, 0);
      }
    }
    
    return availableSlots;
  } catch (error) {
    console.error('Error getting available slots:', error);
    throw new Error('Failed to get available time slots');
  }
}

// Create a calendar event
export async function createCalendarEvent(
  accessToken: string,
  refreshToken: string,
  eventDetails: {
    summary: string;
    description?: string;
    startTime: Date;
    endTime: Date;
    attendeeEmail?: string;
    attendeeName?: string;
  },
  calendarId = 'primary'
) {
  try {
    const calendar = createCalendarClient(accessToken, refreshToken);
    
    const event = {
      summary: eventDetails.summary,
      description: eventDetails.description,
      start: {
        dateTime: eventDetails.startTime.toISOString(),
        timeZone: 'Europe/Vilnius', // Adjust to your timezone
      },
      end: {
        dateTime: eventDetails.endTime.toISOString(),
        timeZone: 'Europe/Vilnius',
      },
      attendees: eventDetails.attendeeEmail ? [
        {
          email: eventDetails.attendeeEmail,
          displayName: eventDetails.attendeeName
        }
      ] : [],
      reminders: {
        useDefault: false,
        overrides: [
          { method: 'email', minutes: 24 * 60 }, // 24 hours before
          { method: 'popup', minutes: 30 }, // 30 minutes before
        ],
      },
    };
    
    const response = await calendar.events.insert({
      calendarId,
      requestBody: event,
      sendUpdates: 'all' // Send email invitations
    });
    
    return response.data;
  } catch (error) {
    console.error('Error creating calendar event:', error);
    throw new Error('Failed to create calendar event');
  }
}

// Update calendar event
export async function updateCalendarEvent(
  accessToken: string,
  refreshToken: string,
  eventId: string,
  eventDetails: {
    summary?: string;
    description?: string;
    startTime?: Date;
    endTime?: Date;
  },
  calendarId = 'primary'
) {
  try {
    const calendar = createCalendarClient(accessToken, refreshToken);
    
    const updateData: any = {};
    
    if (eventDetails.summary) updateData.summary = eventDetails.summary;
    if (eventDetails.description) updateData.description = eventDetails.description;
    if (eventDetails.startTime) {
      updateData.start = {
        dateTime: eventDetails.startTime.toISOString(),
        timeZone: 'Europe/Vilnius',
      };
    }
    if (eventDetails.endTime) {
      updateData.end = {
        dateTime: eventDetails.endTime.toISOString(),
        timeZone: 'Europe/Vilnius',
      };
    }
    
    const response = await calendar.events.update({
      calendarId,
      eventId,
      requestBody: updateData,
      sendUpdates: 'all'
    });
    
    return response.data;
  } catch (error) {
    console.error('Error updating calendar event:', error);
    throw new Error('Failed to update calendar event');
  }
}

// Delete calendar event
export async function deleteCalendarEvent(
  accessToken: string,
  refreshToken: string,
  eventId: string,
  calendarId = 'primary'
) {
  try {
    const calendar = createCalendarClient(accessToken, refreshToken);
    
    await calendar.events.delete({
      calendarId,
      eventId,
      sendUpdates: 'all'
    });
    
    return true;
  } catch (error) {
    console.error('Error deleting calendar event:', error);
    throw new Error('Failed to delete calendar event');
  }
}

// Refresh access token using refresh token
export async function refreshAccessToken(refreshToken: string) {
  try {
    const oauth2Client = createOAuth2Client();
    oauth2Client.setCredentials({ refresh_token: refreshToken });
    
    const { credentials } = await oauth2Client.refreshAccessToken();
    return credentials;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw new Error('Failed to refresh access token');
  }
}
