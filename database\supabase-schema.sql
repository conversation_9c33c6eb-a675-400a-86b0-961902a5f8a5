-- Calendar tokens table to store Google OAuth tokens
CREATE TABLE IF NOT EXISTS calendar_tokens (
  id TEXT PRIMARY KEY DEFAULT 'admin',
  access_token TEXT NOT NULL,
  refresh_token TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bookings table to store appointment information
CREATE TABLE IF NOT EXISTS bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id TEXT NOT NULL, -- Google Calendar event ID
  attendee_name TEXT NOT NULL,
  attendee_email TEXT NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  summary TEXT,
  description TEXT,
  status TEXT DEFAULT 'confirmed' CHECK (status IN ('confirmed', 'cancelled', 'rescheduled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Calendar settings table for business configuration
CREATE TABLE IF NOT EXISTS calendar_settings (
  id TEXT PRIMARY KEY DEFAULT 'default',
  working_hours_start INTEGER DEFAULT 9, -- 9 AM
  working_hours_end INTEGER DEFAULT 17, -- 5 PM
  slot_duration INTEGER DEFAULT 30, -- 30 minutes
  timezone TEXT DEFAULT 'Europe/Vilnius',
  allow_weekends BOOLEAN DEFAULT FALSE,
  advance_booking_days INTEGER DEFAULT 30, -- How many days in advance can people book
  buffer_time INTEGER DEFAULT 0, -- Buffer time between meetings in minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default settings
INSERT INTO calendar_settings (id) VALUES ('default') ON CONFLICT (id) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_start_time ON bookings(start_time);
CREATE INDEX IF NOT EXISTS idx_bookings_attendee_email ON bookings(attendee_email);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_event_id ON bookings(event_id);

-- Enable Row Level Security (RLS) if needed
-- ALTER TABLE calendar_tokens ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE calendar_settings ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for your security requirements)
-- CREATE POLICY "Allow public read access to calendar_settings" ON calendar_settings FOR SELECT USING (true);
-- CREATE POLICY "Allow public insert access to bookings" ON bookings FOR INSERT WITH CHECK (true);
-- CREATE POLICY "Allow public read access to bookings" ON bookings FOR SELECT USING (true);
